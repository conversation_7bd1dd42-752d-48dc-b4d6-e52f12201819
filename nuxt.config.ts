// https://nuxt.com/docs/api/configuration/nuxt-config

export default defineNuxtConfig({
  future: {
    compatibilityVersion: 4,
  },
  compatibilityDate: '2025-05-09',
  devtools: { enabled: true },
  modules: [
    '@formkit/auto-animate/nuxt',
    '@nuxt/ui-pro',
    '@nuxt/image',
    '@nuxtjs/i18n',
    '@vueuse/nuxt',
    'nuxt-charts',
    'nuxt-easy-lightbox',
    '@unlok-co/nuxt-stripe',
    'nuxt-umami'
  ],
  routeRules: {
    '/*/creator/': { redirect: '/creator/ip' },
    '/creator/': { redirect: '/creator/ip' },
    '/*/owner/': { redirect: '/owner/ip' },
    '/owner/': { redirect: '/owner/ip' },
    '/*/fan/**': { ssr: false },
    '/*/creator/**': { ssr: false },
    '/*/owner/**': { ssr: false },
  },
  i18n: {
    strategy: 'prefix',
    defaultLocale: 'en',
    bundle: {
      optimizeTranslationDirective: false,
    },
    baseUrl: process.env.NUXT_PUBLIC_DOMAIN_URL,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'no prefix',
    },
    locales: [
      {
        code: 'en',
        iso: 'en-US',
        language: 'English',
        name: 'English',
        file: 'en.json',
      },
      {
        code: 'jp',
        iso: 'ja-JP',
        language: 'Japanese',
        name: '日本語',
        file: 'ja.json',
      },
      {
        code: 'kr',
        iso: 'ko-KR',
        language: 'Korean',
        name: '한국어',
        file: 'ko.json',
      },
    ],
    experimental: {
      typedOptionsAndMessages: 'all',
    },
  },
  colorMode: {
    fallback: 'dark',
    preference: 'dark',
  },
  css: ['~/assets/css/main.css'],
  image: {
    domains: ['smg-ipgo-dev.s3.ap-southeast-1.amazonaws.com'],
  },
  experimental: {
    typedPages: true,
  },
  runtimeConfig: {
    aws: {
      region: 'ap-northeast-1',
      accessKeyId: 'MISSING AWS ACCESS KEY ID',
      secretAccessKey: 'MISSING AWS SECRET ACCESS KEY',
    },
    old:{
      backendApi: 'https://staging-backend.ipgo.space',
    },
    directus: {
      adminKey: 'MISSING DIRECTUS ADMIN KEY',
    },
    email: {
      defaultFrom: '<EMAIL>',
    },
    // Stripe Secret Key
    stripe: {
      key: 'MISSING-STRIPE-SECRET-KEY',
      options: {},
    },
    public: {
      directus: {
        baseUrl: 'https://stg-admin.ipgo.space',
      },
      domainUrl: 'http://localhost:3000',
      // Stripe Public Key
      stripe: {
        key: 'MISSING-STRIPE-PUBLIC-KEY',
        options: {},
      },
    },
  }
});