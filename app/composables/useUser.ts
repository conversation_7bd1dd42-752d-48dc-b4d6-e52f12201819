export default function () {
  const accessToken = useCookie('access_token');
  const refreshToken = useCookie('refresh_token');

  const profile = useState<DirectusUsers | null>('profileData', () => null);

  const logout = async () => {
    try {
      await $fetch('/api/auth/logout');
    } catch (e) {
      console.error(e);
    }
    accessToken.value = null;
    refreshToken.value = null;
    profile.value = null;
  };

  // const refresh = useAsyncData('refreshToken', async () => $fetch('/api/auth/refresh-token'), {
  //   dedupe: 'defer',
  //   immediate: false,
  // });

  const checkAndRedirectRoles = () => {
    const localePath = useLocalePath();

    // Check if profile and role exist before accessing name property
    if (!profile.value?.role) return;

    const currentRole = (profile.value.role as DirectusRoles).name.toLocaleLowerCase()
    if (currentRole !== 'fan') {
      switch (currentRole) {
        case 'creator':
          navigateTo(localePath('creator-ip'));
          break;
        case 'owner':
          navigateTo(localePath('owner-ip'));
          break;
      }
    }
  }

  const leftOffUrl = useCookie('leftOffUrl');

  return {
    accessToken,
    checkAndRedirectRoles,
    refreshToken,
    profile,
    leftOffUrl,
    logout,
  };
}
